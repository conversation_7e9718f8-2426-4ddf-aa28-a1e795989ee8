<?php

class MockGraphQLResolver {
    private $mockData;
    
    public function __construct() {
        $this->mockData = json_decode(file_get_contents(__DIR__ . '/../mock_data.json'), true);
    }

    public function getProducts() {
        return $this->mockData['products'];
    }

    public function getProduct($variables) {
        $id = $variables['id'];
        foreach ($this->mockData['products'] as $product) {
            if ($product['id'] == $id) {
                return $product;
            }
        }
        return null;
    }

    public function getAttributes($variables) {
        $id = $variables['id'];
        foreach ($this->mockData['products'] as $product) {
            if ($product['id'] == $id) {
                return json_encode($product['attributes']);
            }
        }
        return '[]';
    }

    public function getCart() {
        return $this->mockData['cart'];
    }

    public function addToCart($variables) {
        $productId = $variables['productId'];
        $quantity = $variables['quantity'] ?? 1;
        
        // Find the product
        $product = null;
        foreach ($this->mockData['products'] as $p) {
            if ($p['id'] == $productId) {
                $product = $p;
                break;
            }
        }
        
        if (!$product) {
            throw new Exception("Product not found");
        }

        // Check if item already exists in cart
        $found = false;
        foreach ($this->mockData['cart'] as &$cartItem) {
            if ($cartItem['product']['id'] == $productId) {
                $cartItem['quantity'] += $quantity;
                $found = true;
                break;
            }
        }

        // If not found, add new item
        if (!$found) {
            $this->mockData['cart'][] = [
                'id' => uniqid(),
                'product' => $product,
                'quantity' => $quantity
            ];
        }

        // Save back to file
        file_put_contents(__DIR__ . '/../mock_data.json', json_encode($this->mockData, JSON_PRETTY_PRINT));

        return end($this->mockData['cart']);
    }

    public function updateCart($variables) {
        $productId = $variables['productId'];
        $quantity = $variables['quantity'];
        
        foreach ($this->mockData['cart'] as &$cartItem) {
            if ($cartItem['product']['id'] == $productId) {
                if ($quantity <= 0) {
                    // Remove item
                    $this->mockData['cart'] = array_filter($this->mockData['cart'], function($item) use ($productId) {
                        return $item['product']['id'] != $productId;
                    });
                } else {
                    $cartItem['quantity'] = $quantity;
                }
                break;
            }
        }

        // Save back to file
        file_put_contents(__DIR__ . '/../mock_data.json', json_encode($this->mockData, JSON_PRETTY_PRINT));

        return $this->mockData['cart'];
    }

    public function placeOrder() {
        // Clear the cart
        $this->mockData['cart'] = [];
        file_put_contents(__DIR__ . '/../mock_data.json', json_encode($this->mockData, JSON_PRETTY_PRINT));
        
        return [
            'success' => true,
            'message' => 'Order placed successfully!'
        ];
    }
}
